# Testing

⚠️ **Testing Status**: The testing framework is temporarily disabled while the LLaOA codebase undergoes active development and refactoring. Testing scripts will be rebuilt once the core development is completed.

## Current Status

The LLaOA project is currently in active development with significant architectural changes being made. To maintain development velocity and avoid outdated test maintenance, the testing infrastructure has been temporarily removed.

### What was removed:
- Unit test suites
- Model component tests  
- Integration tests
- Main test runner (`run_tests.py`)

### Future Plans

Once core development stabilizes, we will implement a comprehensive testing framework that includes:

- **Unit Tests**: Testing individual components and functions
- **Integration Tests**: Testing component interactions and workflows
- **Model Tests**: Validating model training and inference
- **Data Tests**: Ensuring data pipeline correctness
- **Performance Tests**: Benchmarking and optimization validation

## Manual Testing During Development

For manual testing during development, you can verify core functionality:

### Test Core Component Imports

```bash
# Test core model imports
uv run python -c "
import sys; sys.path.append('.')
from llaoa.model.language_model import LlaoaLlamaForCausalLM
from llaoa.model.omics_encoder.compass_encoder import COMPASSOmicsTower
print('✓ Core LLAMA + COMPASS components working')
"

# Test builder functions
uv run python -c "
import sys; sys.path.append('.')
from llaoa.model.builder import get_model_class, identify_model_type
print('✓ Model builder functions working')
"
```

### Test Data Loading

```bash
# Test data utilities
uv run python -c "
import sys; sys.path.append('.')
from llaoa.data.dataset import LlaoaDataset
print('✓ Data loading utilities working')
"
```

### Test Training Pipeline

```bash
# Quick validation run (if data available)
uv run python run_train.py \
    --model_path microsoft/DialoGPT-medium \
    --omics_tower_path ./models/compass_model \
    --output_dir ./test_output \
    --tune_omics_projector true \
    --tune_language_model false \
    --tune_omics_encoder false \
    --num_train_epochs 1 \
    --per_device_train_batch_size 1 \
    --max_steps 10 \
    --logging_steps 5
```

## Development Guidelines

While formal testing is disabled, follow these practices for code quality:

### Code Validation

1. **Import Testing**: Always test that new modules can be imported
2. **Function Testing**: Manually verify new functions work as expected
3. **Integration Testing**: Test components work together
4. **Error Handling**: Verify graceful error handling

### Quality Assurance

```bash
# Code formatting with pre-commit
uv run pre-commit run --all-files

# Static analysis (if configured)
uv run flake8 llaoa/
uv run mypy llaoa/
```

### Documentation Testing

```bash
# Verify documentation builds
uv run python -c "
import llaoa
help(llaoa.model.builder)
"
```

## Best Practices During Development

1. **Small Changes**: Make incremental changes and test manually
2. **Documentation**: Keep docstrings updated
3. **Error Handling**: Include proper error messages and validation
4. **Logging**: Use appropriate logging levels for debugging
5. **Dependencies**: Verify new dependencies work correctly

## Reporting Issues

If you encounter issues during manual testing:

1. **Check Prerequisites**: Ensure UV is installed and dependencies are synced
2. **Verify Data**: Ensure data files are in correct format
3. **Check Logs**: Review error messages and stack traces
4. **Document Issues**: Record steps to reproduce problems

## Future Testing Framework

The planned testing framework will include:

### Test Categories

- **Unit Tests**: Individual function/class testing
- **Integration Tests**: Component interaction testing  
- **Model Tests**: Training and inference validation
- **Data Tests**: Data loading and processing verification
- **Performance Tests**: Speed and memory usage benchmarks

### Test Coverage

- **Core Models**: LLAMA, COMPASS, projectors
- **Data Pipeline**: Loading, preprocessing, batching
- **Training**: All training strategies and configurations
- **Evaluation**: Metrics calculation and reporting
- **API**: Public interface functionality

### Continuous Integration

- **Automated Testing**: Tests run on code changes
- **Multi-Environment**: Testing across different Python versions
- **GPU Testing**: Validation on different GPU configurations
- **Documentation**: Automatic documentation testing

## Contributing

When the testing framework is restored:

1. **Test Requirements**: All new features must include tests
2. **Test Coverage**: Maintain high test coverage (>90%)
3. **Test Quality**: Tests should be clear, fast, and reliable
4. **Documentation**: Test documentation and examples

---

**Status**: Testing framework temporarily disabled during active development.
**Timeline**: Will be restored once core development stabilizes.
**Contact**: See GitHub issues for questions or concerns.
