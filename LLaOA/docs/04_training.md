# Training Guide for LLaOA

This guide provides detailed instructions for training LLaOA models on omics data using the UV package manager.

## Prerequisites

Before training, ensure you have:
- Installed LLaOA following the [UV Installation Guide](02a_uv_installation.md)
- COMPASS is properly installed and accessible
- Prepared your omics data and QA pairs (see [Data Preparation Guide](03_data_preparation.md))
- Access to a CUDA-compatible GPU (recommended)

## Training Methods

#### Basic Training

```bash
# Using uv run
uv run python run_train.py \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

#### Training with Extras

```bash
# Install training dependencies if not already done
uv sync --extra train

# Run training with wandb logging
uv run --extra train python run_train.py \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3 \
    --report-to wandb \
    --run-name "llaoa-experiment-1"
```


## Key Training Parameters

- `--language-model`: Base language model to use (e.g., "meta-llama/Llama-2-7b-hf")
- `--compass-model-path`: Path to the pre-trained COMPASS model
- `--rna-seq-path`: Path to the RNAseq data file
- `--qa-json-path`: Path to the QA pairs JSON file
- `--output-dir`: Directory to save model checkpoints
- `--per-device-train-batch-size`: Batch size per GPU
- `--learning-rate`: Learning rate for optimization
- `--num-train-epochs`: Number of training epochs

## Precision and Compatibility

### COMPASS Precision Configuration

LLaOA automatically configures COMPASS to use **float16 precision** instead of the original float32. This provides several benefits:

- **Mixed Precision Compatibility**: Prevents "Attempting to unscale FP16 gradients" errors during training
- **Memory Efficiency**: Reduces GPU memory usage by approximately 50%
- **Consistent dtype**: Ensures all model components use the same precision
- **Performance**: Maintains model quality while improving training speed

**Technical Details**:
- COMPASS models are automatically converted to float16 during initialization
- All COMPASS parameters and computations use float16 precision
- The change is transparent and requires no user configuration
- Feature extraction quality remains equivalent to float32

### Troubleshooting Precision Issues

If you encounter precision-related errors:

1. **"Attempting to unscale FP16 gradients"**: This is automatically prevented by LLaOA's float16 configuration
2. **Memory issues**: The float16 conversion reduces memory usage significantly
3. **Compatibility**: All components (language model, COMPASS, projector) use consistent precision

## Advanced Training Options

### Parameter-Efficient Fine-Tuning (LoRA)

For more efficient training, especially with limited GPU memory:

#### Native uv
```bash
uv run python run_train.py \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_lora" \
    --per-device-train-batch-size 8 \
    --learning-rate 2e-4 \
    --num-train-epochs 5 \
    --lora-rank 16 \
    --lora-alpha 32 \
    --lora-dropout 0.05 \
    --lora-target-modules "q_proj" "k_proj" "v_proj" "o_proj"
```

### Mixed Precision Training

To speed up training and reduce memory usage:

**Important**: LLaOA automatically configures COMPASS to use float16 precision for optimal compatibility with mixed precision training. This differs from the original COMPASS implementation and prevents potential "Attempting to unscale FP16 gradients" errors.

#### Native uv
```bash
uv run python run_train.py \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_fp16" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3 \
    --fp16
```

### Distributed Training

For multi-GPU training:

#### Native uv
```bash
uv run torchrun --nproc_per_node=4 run_train.py \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_distributed" \
    --per-device-train-batch-size 2 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```


## Customizing the Training Process

### Feature Type Selection

Choose the type of features to extract from COMPASS:

```bash
# Using uv
uv run python run_train.py \
    --feature-type "concept_level" \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_concept"


Options for `--feature-type`:
- `gene_level`: Raw gene expression features
- `geneset_level`: Gene set enrichment features
- `concept_level`: High-level biological concept features
- `vector`: Raw vector representation

### Projector Type Selection

Choose the type of projector to map omics features to language model space:

```bash
# Using uv
uv run python run_train.py \
    --projector-type "mlp3x_gelu" \
    --language-model "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_mlp3x"


Options for `--projector-type`:
- `linear`: Simple linear projection
- `mlp2x_gelu`: Two-layer MLP with GELU activation
- `mlp3x_gelu`: Three-layer MLP with GELU activation

## Configuration Files

Create configuration files for reproducible training:

```yaml
# config.yaml
language_model: "meta-llama/Llama-2-7b-hf"
compass_model_path: "./COMPASS/example/model/pretrainer.pt"
rna_seq_path: "COMPASS/example/data/compass_gide_tpm.tsv"
qa_json_path: "data/omics_qa.json"
output_dir: "./checkpoints"
per_device_train_batch_size: 4
learning_rate: 1e-5
num_train_epochs: 3
feature_type: "concept_level"
projector_type: "mlp2x_gelu"
logging_steps: 5
eval_steps: 50
save_steps: 100
fp16: true
report_to: "wandb"
```

Use with:
```bash
# Native
uv run python run_train.py --config config.yaml
```

## Monitoring Training

### With Weights & Biases

```bash
# Set API key
export WANDB_API_KEY="your-api-key"

# Train with logging
uv run python run_train.py \
    --config config.yaml \
    --report-to wandb \
    --run-name "llaoa-experiment-1"
```

### Custom Logging

Customize logging and checkpoint frequency:

```bash
uv run python run_train.py \
    --config config.yaml \
    --logging-steps 5 \
    --eval-steps 50 \
    --save-steps 100 \
    --logging-dir "./logs"
```

## Development Workflows

### Interactive Development

```bash
uv run --extra dev jupyter lab
```

### Testing Configuration

```bash
# Quick test run
uv run python run_train.py \
    --config test_config.yaml \
    --max-steps 10 \
    --output-dir "./test_checkpoints"


## Next Steps

After training your model:
- Evaluate its performance using the [Evaluation Guide](05_evaluation.md)
- Use it for inference as described in the example notebooks
- Fine-tune based on evaluation results
