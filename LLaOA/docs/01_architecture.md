# LLaOA Architecture Overview

LLaOA (Large Language and Omics Assistant) is a framework designed to integrate omics data with large language models. It follows a modular architecture inspired by LLaVA (Large Language and Vision Assistant), replacing the vision encoder with an omics encoder.

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Omics Encoder  │───►│    Projector    │───►│ Language Model  │
│    (COMPASS)    │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

The architecture consists of three main components:

1. **Omics Encoder**: Processes omics data (e.g., RNAseq) and extracts meaningful features
2. **Projector**: Maps omics features to the language model's embedding space
3. **Language Model**: Generates responses based on the projected omics features and text prompts

## Component Details

### Omics Encoder

The omics encoder is responsible for processing raw omics data and extracting meaningful features. The default implementation uses COMPASS, a state-of-the-art encoder for omics data.

Key features:
- Support for gene-level, geneset-level, and concept-level features
- Pre-trained on large omics datasets
- Ability to handle various omics data types
- **Optimized for float16 precision**: LLaOA configures COMPASS to use float16 by default for consistency with mixed precision training and improved memory efficiency

**Technical Note**: Unlike the original COMPASS implementation which uses float32, LLaOA automatically sets COMPASS to float16 precision. This provides:
- Better compatibility with mixed precision training workflows
- Reduced GPU memory usage during training
- Consistent dtype handling across all model components
- Prevention of "Attempting to unscale FP16 gradients" errors

Implementation: `llaoa/model/omics_encoder/compass_encoder.py`

### Projector

The projector maps the features from the omics encoder to the language model's embedding space. This is a critical component that enables the language model to understand and reason about omics data.

Available projector types:
- `linear`: Simple linear projection
- `mlp2x_gelu`: Two-layer MLP with GELU activation
- `mlp3x_gelu`: Three-layer MLP with GELU activation

Implementation: `llaoa/model/omics_projector/builder.py`

### Language Model

The language model generates responses based on the projected omics features and text prompts. LLaOA supports various language models:

- LLaMA (Meta's open-source language model family)

Implementation:
- `llaoa/model/language_model/llaoa_llama.py`

## Data Flow

1. **Input**: RNAseq data and a text question
2. **Processing**:
   - The omics encoder processes the RNAseq data to extract features
   - The projector maps these features to the language model's embedding space
   - The language model receives both the projected features and the text question
3. **Output**: The language model generates a response that answers the question based on the omics data

## Integration with Hugging Face

LLaOA is built on top of the Hugging Face Transformers library, making it compatible with a wide range of pre-trained language models and tools.

## Extensibility

The modular architecture of LLaOA makes it highly extensible:

- New omics encoders can be added by implementing the encoder interface
- New projector types can be added to optimize the mapping between omics features and language model embeddings
- Support for additional language models can be added by implementing the language model interface

For more details on extending LLaOA, see the [Customization Guide](07_customization.md).
