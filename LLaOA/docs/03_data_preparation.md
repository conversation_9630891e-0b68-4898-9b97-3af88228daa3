# Data Preparation Guide for LLaOA

This guide explains how to prepare omics data and question-answer pairs for use with LLaOA.

## Data Requirements

LLaOA requires two main types of data:

1. **Omics Data**: Gene expression data (e.g., RNAseq)
2. **Question-Answer Pairs**: Questions about the omics data and their corresponding answers

## Preparing Omics Data

### RNAseq Data Format

LLaOA expects RNAseq data in a tab-separated values (TSV) format with the following structure:

- Rows represent samples
- Columns represent genes
- The first column can optionally contain sample IDs
- Gene expression values should be normalized (e.g., TPM, FPKM, or log-transformed counts)

Example:

```
sample_id   GENE1   GENE2   GENE3   ...
sample1     10.5    2.3     0.0     ...
sample2     5.2     1.7     3.1     ...
...
```

### Data Preprocessing

1. **Normalization**: Ensure your gene expression data is properly normalized
2. **Missing Values**: Handle missing values (e.g., by imputation or filtering)
3. **Gene Selection**: Consider filtering to include only relevant genes

You can use the provided data processing utilities:

```python
from llaoa.data.processing import normalize_expression, filter_genes

# Normalize expression data
normalized_data = normalize_expression(data, method='log2')

# Filter genes by variance
filtered_data = filter_genes(normalized_data, method='variance', top_n=5000)
```

## Preparing Question-Answer Pairs

### QA JSON Format

Question-answer pairs should be provided in a JSON file with the following format:

```json
[
  {
    "sample_id": "sample1",
    "question": "What is the predicted response to immunotherapy for this patient?",
    "answer": "Based on the gene expression profile, this patient is likely to respond to immunotherapy."
  },
  {
    "sample_id": "sample2",
    "question": "Analyze the immune profile of this patient based on gene expression.",
    "answer": "The patient shows elevated expression of immune-related genes, suggesting an inflamed tumor microenvironment."
  },
  ...
]
```

Each entry in the JSON array should contain:
- `sample_id`: Identifier matching a sample in the omics data
- `question`: The question to ask about the sample
- `answer`: The expected answer

### Creating Effective QA Pairs

For optimal results:

1. **Diverse Questions**: Include a variety of question types (e.g., prediction, analysis, comparison)
2. **Specific Answers**: Provide detailed, specific answers based on the omics data
3. **Domain Knowledge**: Incorporate domain knowledge in the answers
4. **Consistent Format**: Maintain a consistent format for similar questions

## Data Splitting

LLaOA automatically splits your data into training and validation sets based on the `validation_split` parameter. To ensure reproducibility, set the `seed` parameter.

```bash
python run_train.py --validation-split 0.1 --seed 42
```

## Sample Data

LLaOA includes sample data for testing:

- RNAseq data: `COMPASS/example/data/compass_gide_tpm.tsv`
- QA pairs: `data/omics_qa.json`

You can use these as templates for your own data.

## Advanced Data Preparation

### Custom Sample ID Column

If your RNAseq data uses a different column for sample IDs, specify it using the `sample_id_col` parameter:

```bash
python run_train.py --sample-id-col "patient_identifier"
```

### Data Augmentation

To increase the diversity of your training data, consider:

1. **Paraphrasing Questions**: Create variations of the same question
2. **Adding Noise**: Introduce small variations in the omics data
3. **Combining Samples**: Create synthetic samples by combining existing ones

## Next Steps

After preparing your data:
- Proceed to [Training Guide](04_training.md) to train your model
- Check the [Example Notebooks](../examples/) for interactive examples
