# LLaOA Installation with UV Package Manager

This guide provides comprehensive instructions for installing and using LLaOA with the `uv` package manager for native development.

## What is uv?

`uv` is an extremely fast Python package and project manager written in Rust, offering:

- **10-100x faster** dependency resolution and installation
- **Reproducible builds** with lock files
- **Better dependency resolution** with conflict detection
- **Virtual environment management**
- **Project scaffolding** and workspace management

## Prerequisites

- Python 3.8 or higher (3.10 recommended)
- CUDA-compatible GPU (recommended for training)
- Git

## Step 1: Install uv Package Manager

### Installation Options

```bash
# On macOS and Linux (recommended)
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Using pip (alternative)
pip install uv

# Using conda
conda install -c conda-forge uv

# Using homebrew (macOS)
brew install uv
```

### Verify Installation

```bash
uv --version
# Should show version 0.1.0 or higher
```

If `uv` command is not found, add it to your PATH:

```bash
# Add to ~/.bashrc or ~/.zshrc
export PATH="$HOME/.cargo/bin:$PATH"
source ~/.bashrc
```

## Step 2: Clone and Setup LLaOA

### Clone Repository

```bash
git clone https://github.com/azu-oncology-rd/LLaOA.git
cd LLaOA
```

### Initialize Project

```bash
# Install all dependencies
uv sync --extra all

# Or install specific dependency groups
uv sync                    # Core dependencies only
uv sync --extra train      # Core + training dependencies
uv sync --extra bio        # Core + bioinformatics dependencies
uv sync --extra dev        # Core + development tools
```

This will:
- Read `pyproject.toml`
- Create a virtual environment in `.venv/`
- Install all specified dependencies
- Generate `uv.lock` file for reproducible builds

## Step 3: Verify Installation

### Test Basic Installation

```bash
# Test Python environment
uv run python -c "import llaoa; print('LLaOA installed successfully!')"

# Test CUDA (if available)
uv run python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# Test core components (testing framework temporarily disabled)
uv run python -c "import sys; sys.path.append('.'); from llaoa.model.language_model import LlaoaLlamaForCausalLM; print('✓ Core components working')"
```

### Test Key Dependencies

```bash
# Test core ML dependencies
uv run python -c "import torch, transformers, pandas, numpy, sklearn; print('Core dependencies OK')"

# Test training dependencies
uv run python -c "import wandb, datasets, accelerate; print('Training dependencies OK')"

# Test bioinformatics dependencies
uv run python -c "import matplotlib, seaborn; print('Visualization dependencies OK')"
```

## Step 4: Install COMPASS (Required)

LLaOA requires COMPASS for omics data encoding:

```bash
# Clone COMPASS in a separate directory
cd ..
git clone https://github.com/mims-harvard/COMPASS.git
cd COMPASS

# Install COMPASS dependencies
uv venv
source .venv/bin/activate
uv pip install -r requirements.txt
cd ../LLaOA
```

## Common Development Workflows

### Running Applications

```bash
# Run training
uv run python run_train.py --config config.yaml

# Run evaluation
uv run python run_eval.py --llaoa-checkpoint ./checkpoints/final

# Run data processing
uv run python process_data.py --input data/ --output processed/

# Manual testing (comprehensive testing temporarily disabled)
# See docs/06_testing.md for manual testing instructions
```

### Managing Dependencies

```bash
# Add a new core dependency
uv add numpy>=1.21.0

# Add a training dependency
uv add --group train torch-audio>=0.13.0

# Add a development dependency
uv add --group dev pytest-xdist>=3.0.0

# Remove a dependency
uv remove package-name

# Update all dependencies
uv lock --upgrade

# Update specific package
uv lock --upgrade-package torch
```

### Working with Virtual Environments

```bash
# Activate environment manually (optional)
source .venv/bin/activate

# Or use uv run for individual commands
uv run python script.py

# Show environment information
uv info

# Create new environment (if needed)
uv venv --python 3.10

# Clean environment
rm -rf .venv && uv sync --extra all
```

## Development Setup

### Code Quality Tools

```bash
# Install development dependencies
uv sync --extra dev

# Setup pre-commit hooks
uv run pre-commit install

# Run formatting
uv run black .
uv run isort .

# Run linting
uv run flake8 .
uv run mypy .

# Run tests with coverage
uv run pytest --cov=llaoa
```

### Jupyter Development

```bash
# Start Jupyter Lab
uv run --extra dev jupyter lab
```

### Interactive Development

```bash
# Python REPL with environment
uv run python

# IPython with environment
uv run --extra dev ipython
```

## Configuration

### Environment Variables

Set these for optimal performance:

```bash
# CUDA settings
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6"

# HuggingFace cache
export HF_HOME="/path/to/hf_cache"
export HF_TOKEN="your-hf-token"

# Weights & Biases
export WANDB_API_KEY="your-api-key"

# uv configuration
export UV_CACHE_DIR="/path/to/uv-cache"
```

### GPU Setup

For CUDA support:

```bash
# Check CUDA version
nvidia-smi

# Install CUDA-specific PyTorch (if needed)
uv add --index-url https://download.pytorch.org/whl/cu118 \
    torch==2.1.2+cu118 \
    torchvision==0.16.2+cu118 \
    torchaudio==2.1.2+cu118
```

## Project Structure

After installation, your project structure should look like:

```
LLaOA/
├── .venv/                 # Virtual environment (auto-created)
├── pyproject.toml        # Project configuration
├── uv.lock              # Dependency lock file
├── llaoa/               # Main package
├── run_train.py         # Training script
├── run_eval.py          # Evaluation script
├── run_tests.py         # Test script
└── docs/                # Documentation
```

## Troubleshooting

### Common Issues

1. **uv not found after installation**
   ```bash
   # Reload shell configuration
   source ~/.bashrc
   # Or add to PATH manually
   export PATH="$HOME/.cargo/bin:$PATH"
   ```

2. **CUDA compatibility issues**
   ```bash
   # Check PyTorch CUDA version
   uv run python -c "import torch; print(torch.version.cuda)"
   
   # Reinstall with correct CUDA version
   uv remove torch torchvision torchaudio
   uv add --index-url https://download.pytorch.org/whl/cu118 torch torchvision torchaudio
   ```

3. **Dependency conflicts**
   ```bash
   # Clean and reinstall
   rm -rf .venv uv.lock
   uv lock
   uv sync --extra all
   ```

4. **Permission issues**
   ```bash
   # Ensure proper permissions
   chmod -R 755 .venv/
   ```

5. **Memory issues during training**
   - Reduce batch size in training scripts
   - Use gradient accumulation
   - Enable mixed precision training (fp16)

### Lock File Issues

```bash
# Regenerate lock file
uv lock --upgrade

# Force refresh of all packages
rm uv.lock && uv lock

# Clean uv cache
uv cache clean
```

## Performance Tips

- **Use lock files**: Always commit `uv.lock` for reproducible builds
- **Dependency groups**: Use `--extra` flags to install only needed dependencies
- **Cache management**: Let uv handle caching automatically
- **Parallel installation**: uv installs packages in parallel by default
- **Environment reuse**: Use `uv run` to avoid manual activation

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: LLaOA Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Install uv
      uses: astral-sh/setup-uv@v1
      with:
        version: "latest"
    
    - name: Set up Python
      run: uv python install 3.10
    
    - name: Install dependencies
      run: uv sync --all-extras
    
    - name: Run tests
      run: uv run pytest
```

## Migration from pip/conda

If migrating from pip or conda:

```bash
# Remove old environments
conda deactivate  # if using conda
rm -rf venv/ .conda/  # remove old environments

# Initialize with uv
uv sync --extra all

# Update workflow commands
# Old: python script.py
# New: uv run python script.py
```

## Next Steps

After successful installation:

1. **Prepare data**: Follow the [Data Preparation Guide](03_data_preparation.md)
2. **Train a model**: Use the [Training Guide](04_training.md)
3. **Evaluate models**: See the [Evaluation Guide](05_evaluation.md)
4. **Run tests**: Check the [Testing Guide](06_testing.md)

## Getting Help

- **uv documentation**: https://docs.astral.sh/uv/
- **LLaOA issues**: Check project GitHub issues
- **Command help**: `uv --help` or `uv <command> --help`
- **Project info**: `uv info` and `uv tree`

## Why Choose uv over pip?

| Feature | pip | uv |
|---------|-----|-----|
| Speed | Standard | 10-100x faster |
| Lock files | Manual (requirements.txt) | Automatic (uv.lock) |
| Conflict resolution | Basic | Advanced |
| Virtual environments | Manual management | Automatic |
| Reproducibility | Limited | Excellent |
| Dependency groups | Limited | Full support |
| Cache management | Basic | Intelligent |

LLaOA is optimized for UV package manager development workflows. 