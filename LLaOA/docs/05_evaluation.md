# Evaluation Guide

This guide explains how to evaluate LLaOA models on omics question-answering tasks using the UV package manager.

## Prerequisites

- LLaOA installed following the [UV Installation Guide](02a_uv_installation.md)
- Trained LLaOA model (see [Training Guide](04_training.md))
- Test dataset prepared following [Data Preparation](03_data_preparation.md)
- COMPASS properly installed and accessible

## Evaluation Methods

### Basic Evaluation with UV

Evaluate a trained model on a test dataset:

```bash
# Basic evaluation
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --omics_data_path ./data/test_omics.h5 \
    --output_dir ./evaluation_results \
    --per_device_eval_batch_size 4 \
    --max_new_tokens 128

# Evaluation with specific metrics
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --omics_data_path ./data/test_omics.h5 \
    --output_dir ./evaluation_results \
    --metrics bleu rouge bertscore \
    --generation_mode true \
    --num_beams 4
```

### Advanced Evaluation Configuration

```bash
# Comprehensive evaluation with all metrics
uv run python run_eval.py \
    --model_path ./checkpoints/projector_lm_lora \
    --test_data ./data/test_dataset.json \
    --omics_data_path ./data/test_rnaseq.h5 \
    --output_dir ./evaluation_results/comprehensive \
    --per_device_eval_batch_size 2 \
    --max_new_tokens 256 \
    --generation_mode true \
    --num_beams 4 \
    --metrics bleu rouge bertscore exact_match \
    --save_predictions true \
    --compute_confidence true
```

## Evaluation Metrics

### Text Generation Metrics

#### BLEU Score
Measures n-gram overlap between generated and reference text:
```bash
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --metrics bleu \
    --bleu_weights 0.25 0.25 0.25 0.25
```

#### ROUGE Score
Measures recall-oriented understudy for gisting evaluation:
```bash
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --metrics rouge \
    --rouge_types rouge1 rouge2 rougeL
```

#### BERTScore
Measures semantic similarity using contextual embeddings:
```bash
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --metrics bertscore \
    --bertscore_model bert-base-uncased
```

### Custom Evaluation Scripts

Create custom evaluation scripts for specialized metrics:

```python
# custom_eval.py
import torch
from llaoa.eval.evaluator import LlaoaEvaluator
from llaoa.model.builder import load_pretrained_model

def custom_evaluation():
    # Load model
    model = load_pretrained_model("./checkpoints/my_model")
    
    # Initialize evaluator
    evaluator = LlaoaEvaluator(model)
    
    # Run evaluation
    results = evaluator.evaluate(
        test_data_path="./data/test_set.json",
        omics_data_path="./data/test_omics.h5",
        metrics=["bleu", "rouge", "custom_metric"],
        output_dir="./evaluation_results"
    )
    
    return results

if __name__ == "__main__":
    results = custom_evaluation()
    print(f"Evaluation results: {results}")
```

Run the custom evaluation:
```bash
uv run python custom_eval.py
```

## Evaluation Configuration

### Configuration Parameters

```yaml
# Model configuration
model_path: "./checkpoints/my_model"          # Path to trained model
test_data: "./data/test_set.json"            # Test QA pairs
omics_data_path: "./data/test_omics.h5"      # Test omics data
output_dir: "./evaluation_results"           # Output directory

# Generation settings
generation_mode: true                        # Enable text generation
max_new_tokens: 128                         # Maximum tokens to generate
num_beams: 4                                # Beam search size
do_sample: false                            # Sampling vs beam search
temperature: 1.0                            # Sampling temperature
top_p: 0.9                                  # Nucleus sampling

# Evaluation settings
per_device_eval_batch_size: 4               # Batch size for evaluation
metrics: ["bleu", "rouge", "bertscore"]     # Metrics to compute
save_predictions: true                      # Save generated predictions
compute_confidence: false                   # Compute prediction confidence

# Output settings
save_detailed_results: true                 # Save detailed per-sample results
generate_report: true                       # Generate evaluation report
```

### Metric-Specific Configuration

```yaml
# BLEU configuration
bleu_weights: [0.25, 0.25, 0.25, 0.25]     # N-gram weights
bleu_smoothing: true                        # Enable smoothing

# ROUGE configuration  
rouge_types: ["rouge1", "rouge2", "rougeL"] # ROUGE variants
rouge_use_stemmer: true                     # Enable stemming

# BERTScore configuration
bertscore_model: "bert-base-uncased"        # BERT model for scoring
bertscore_lang: "en"                        # Language
```

## Batch Evaluation

### Multiple Model Comparison

```bash
# Evaluate multiple models
for model in projector_only projector_lm_lora projector_lm_full; do
    echo "Evaluating $model..."
    uv run python run_eval.py \
        --model_path ./checkpoints/$model \
        --test_data ./data/test_set.json \
        --omics_data_path ./data/test_omics.h5 \
        --output_dir ./evaluation_results/$model \
        --metrics bleu rouge bertscore \
        --save_predictions true
done
```

### Cross-Validation Evaluation

```bash
# K-fold cross-validation
for fold in {1..5}; do
    echo "Evaluating fold $fold..."
    uv run python run_eval.py \
        --model_path ./checkpoints/fold_$fold \
        --test_data ./data/fold_$fold/test.json \
        --omics_data_path ./data/fold_$fold/test_omics.h5 \
        --output_dir ./evaluation_results/fold_$fold \
        --metrics bleu rouge bertscore
done
```

## Specialized Evaluations

### Domain-Specific Evaluation

```bash
# Evaluate on specific omics domains
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/cancer_specific_test.json \
    --omics_data_path ./data/cancer_rnaseq.h5 \
    --output_dir ./evaluation_results/cancer_domain \
    --domain_tag "cancer_genomics"
```

### Performance Analysis

```bash
# Detailed performance analysis
uv run python run_eval.py \
    --model_path ./checkpoints/my_model \
    --test_data ./data/test_set.json \
    --omics_data_path ./data/test_omics.h5 \
    --output_dir ./evaluation_results/analysis \
    --analyze_performance true \
    --generate_attention_maps true \
    --compute_feature_importance true
```

## Results Analysis

### Evaluation Output Structure

```
evaluation_results/
├── metrics.json              # Overall metrics
├── predictions.json          # Generated predictions
├── detailed_results.json     # Per-sample results
├── evaluation_report.html    # HTML report
├── attention_maps/           # Attention visualizations
└── plots/                    # Metric plots and charts
```

### Key Metrics Interpretation

#### BLEU Score
- **Range**: 0-100 (higher is better)
- **Good**: >30 for technical text, >50 for general text
- **Excellent**: >70

#### ROUGE Score
- **ROUGE-1**: Unigram overlap (content overlap)
- **ROUGE-2**: Bigram overlap (fluency)
- **ROUGE-L**: Longest common subsequence (structure)
- **Range**: 0-1 (higher is better)

#### BERTScore
- **Precision**: Generated text quality
- **Recall**: Coverage of reference content
- **F1**: Harmonic mean of precision and recall
- **Range**: 0-1 (higher is better)

### Results Visualization

```python
# Visualize evaluation results
import matplotlib.pyplot as plt
import json

# Load results
with open('./evaluation_results/metrics.json', 'r') as f:
    metrics = json.load(f)

# Plot metrics
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

# BLEU scores
axes[0].bar(['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4'], 
            [metrics['bleu_1'], metrics['bleu_2'], 
             metrics['bleu_3'], metrics['bleu_4']])
axes[0].set_title('BLEU Scores')
axes[0].set_ylim(0, 100)

# ROUGE scores
axes[1].bar(['ROUGE-1', 'ROUGE-2', 'ROUGE-L'], 
            [metrics['rouge_1'], metrics['rouge_2'], metrics['rouge_l']])
axes[1].set_title('ROUGE Scores')
axes[1].set_ylim(0, 1)

# BERTScore
axes[2].bar(['Precision', 'Recall', 'F1'], 
            [metrics['bertscore_precision'], 
             metrics['bertscore_recall'], 
             metrics['bertscore_f1']])
axes[2].set_title('BERTScore')
axes[2].set_ylim(0, 1)

plt.tight_layout()
plt.savefig('./evaluation_results/metrics_summary.png')
plt.show()
```

## Troubleshooting

### Common Issues

#### Out of Memory During Evaluation
```bash
# Reduce batch size
--per_device_eval_batch_size 1

# Use gradient checkpointing
--gradient_checkpointing true

# Evaluate in chunks
--eval_chunk_size 100
```

#### Slow Evaluation
```bash
# Increase batch size if memory allows
--per_device_eval_batch_size 8

# Reduce beam size
--num_beams 2

# Disable expensive metrics
--metrics bleu rouge  # Skip bertscore
```

#### Model Loading Issues
```bash
# Verify model path
ls -la ./checkpoints/my_model/

# Check model compatibility
uv run python -c "
from llaoa.model.builder import load_pretrained_model
model = load_pretrained_model('./checkpoints/my_model')
print('Model loaded successfully')
"
```

## Best Practices

1. **Baseline Comparison**: Always compare against relevant baselines
2. **Multiple Metrics**: Use complementary metrics for comprehensive evaluation
3. **Statistical Significance**: Run multiple evaluations and report confidence intervals
4. **Domain-Specific Evaluation**: Test on domain-relevant datasets
5. **Error Analysis**: Analyze failure cases to understand model limitations
6. **Reproducibility**: Save evaluation configurations and random seeds

## Next Steps

After evaluation, consider:
- **Model Analysis**: Investigate attention patterns and feature importance
- **Error Analysis**: Understand common failure modes
- **Domain Adaptation**: Fine-tune for specific omics domains
- **Deployment**: Prepare models for production use

For deployment and production considerations, see the [Customization Guide](07_customization.md).
