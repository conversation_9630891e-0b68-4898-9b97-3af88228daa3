#!/bin/bash

# LLaOA Training Example Script - Projector Only Training
# This script demonstrates end-to-end training of LLaOA models using projector-only training mode
# which freezes the omics encoder and language model while training only the projector layer.

set -e  # Exit on any error

# =============================================================================
# Configuration
# =============================================================================

# Model and data paths will be set based on current directory
# (see directory detection section below)
EXPERIMENT_NAME="llaoa_projector_only"

# Training hyperparameters
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=4
LEARNING_RATE=1e-4
NUM_EPOCHS=3
MAX_LENGTH=512
EVAL_STEPS=100
SAVE_STEPS=200
LOGGING_STEPS=10

# Hardware configuration
NUM_GPUS=$(nvidia-smi -L | wc -l)
if [ $NUM_GPUS -eq 0 ]; then
    echo "Warning: No GPUs detected. Training will be very slow on CPU."
    DEVICE_MAP="cpu"
else
    echo "Detected $NUM_GPUS GPU(s)"
    DEVICE_MAP="auto"
fi

# =============================================================================
# Validation
# =============================================================================

echo "=== LLaOA Training Script ==="
echo "Validating configuration..."

# Check if we're in the correct directory (either LLaOA root or LLaOA/LLaOA)
if [ ! -f "llaoa/__init__.py" ] && [ ! -f "LLaOA/llaoa/__init__.py" ]; then
    echo "Error: Please run this script from the LLaOA root directory or LLaOA/LLaOA directory"
    echo "Current directory: $(pwd)"
    echo "Looking for llaoa/__init__.py or LLaOA/llaoa/__init__.py"
    exit 1
fi

# Adjust paths based on current directory
if [ -f "llaoa/__init__.py" ]; then
    # We're in LLaOA/LLaOA directory
    COMPASS_MODEL_PATH="./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="./models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="./data/dataset_01/qa_pairs.json"
    OUTPUT_DIR="./checkpoints/llaoa_projector_training_$(date +%Y%m%d_%H%M%S)"
else
    # We're in LLaOA root directory
    COMPASS_MODEL_PATH="./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="./models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="./data/dataset_01/qa_pairs.json"
    OUTPUT_DIR="./checkpoints/llaoa_projector_training_$(date +%Y%m%d_%H%M%S)"
fi

# Check model paths
if [ ! -f "$COMPASS_MODEL_PATH" ]; then
    echo "Error: COMPASS model not found at $COMPASS_MODEL_PATH"
    echo "Please ensure the COMPASS model is downloaded and placed correctly"
    exit 1
fi

if [ ! -d "$LANGUAGE_MODEL_PATH" ]; then
    echo "Error: Language model not found at $LANGUAGE_MODEL_PATH"
    echo "Please ensure the LLaMA-2 model is downloaded and placed correctly"
    exit 1
fi

# Check data paths
if [ ! -f "$RNA_SEQ_PATH" ]; then
    echo "Error: RNA-seq data not found at $RNA_SEQ_PATH"
    exit 1
fi

if [ ! -f "$QA_JSON_PATH" ]; then
    echo "Error: Q&A data not found at $QA_JSON_PATH"
    exit 1
fi

echo "✓ All paths validated successfully"

# =============================================================================
# Environment Setup
# =============================================================================

echo "Setting up environment..."

# Create output directory
mkdir -p "$OUTPUT_DIR"
echo "✓ Created output directory: $OUTPUT_DIR"

# Log configuration
cat > "$OUTPUT_DIR/training_config.txt" << EOF
LLaOA Training Configuration
============================
Date: $(date)
Host: $(hostname)
User: $(whoami)
Working Directory: $(pwd)

Model Configuration:
- COMPASS Model: $COMPASS_MODEL_PATH
- Language Model: $LANGUAGE_MODEL_PATH
- Feature Type: gene_level
- Projector Type: mlp2x_gelu

Data Configuration:
- RNA-seq Data: $RNA_SEQ_PATH
- Q&A Data: $QA_JSON_PATH
- Max Length: $MAX_LENGTH
- Sample ID Column: sample_id

Training Configuration:
- Batch Size: $BATCH_SIZE
- Gradient Accumulation Steps: $GRADIENT_ACCUMULATION_STEPS
- Learning Rate: $LEARNING_RATE
- Number of Epochs: $NUM_EPOCHS
- Eval Steps: $EVAL_STEPS
- Save Steps: $SAVE_STEPS
- Output Directory: $OUTPUT_DIR

Hardware:
- GPUs Detected: $NUM_GPUS
- Device Map: $DEVICE_MAP
EOF

echo "✓ Configuration logged to $OUTPUT_DIR/training_config.txt"

# =============================================================================
# Training
# =============================================================================

echo "Starting training..."
echo "Output will be saved to: $OUTPUT_DIR"

# Run training with uv using the updated run_train.py script
# Note: Only using parameters supported by the core training implementation
uv run python run_train.py \
    --model-base "$LANGUAGE_MODEL_PATH" \
    --compass-model-path "$COMPASS_MODEL_PATH" \
    --feature-type "gene_level" \
    --projector-type "mlp2x_gelu" \
    --tune-projector-only \
    --rna-seq-path "$RNA_SEQ_PATH" \
    --qa-json-path "$QA_JSON_PATH" \
    --sample-id-col "sample_id" \
    --max-length $MAX_LENGTH \
    --output-dir "$OUTPUT_DIR" \
    --num-train-epochs $NUM_EPOCHS \
    --per-device-train-batch-size $BATCH_SIZE \
    --gradient-accumulation-steps $GRADIENT_ACCUMULATION_STEPS \
    --learning-rate $LEARNING_RATE \
    --weight-decay 0.01 \
    --warmup-steps 100 \
    --lr-scheduler-type "cosine" \
    --logging-steps $LOGGING_STEPS \
    --eval-steps $EVAL_STEPS \
    --save-steps $SAVE_STEPS \
    --save-total-limit 3 \
    --dataloader-num-workers 4 \
    --fp16 \
    --report-to "none" \
    --seed 42

# =============================================================================
# Post-training
# =============================================================================

echo "Training completed!"
echo "Results saved to: $OUTPUT_DIR"

# Create a summary
cat > "$OUTPUT_DIR/training_summary.txt" << EOF
LLaOA Training Summary
=====================
Training completed at: $(date)
Final model saved to: $OUTPUT_DIR

Next Steps:
1. Evaluate the model using the evaluation script:
   ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR

2. Run inference on new data:
   uv run python run_eval.py --llaoa-checkpoint $OUTPUT_DIR --generation-mode

3. Continue training (if needed):
   Use the same command with --resume_from_checkpoint $OUTPUT_DIR

Model Files:
- config.json: Model configuration
- pytorch_model.bin: Trained model weights
- tokenizer files: Tokenizer configuration
- training_args.bin: Training arguments
- trainer_state.json: Training state

Logs:
- training_config.txt: Initial configuration
- training_summary.txt: This summary
- runs/: TensorBoard logs (if enabled)
EOF

echo "✓ Training summary saved to $OUTPUT_DIR/training_summary.txt"
echo ""
echo "=== Training Complete ==="
echo "Model saved to: $OUTPUT_DIR"
echo "To evaluate the model, run:"
echo "  ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR"